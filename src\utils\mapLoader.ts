import AMapLoader from '@amap/amap-jsapi-loader'

class MapLoader {
  private static instance: MapLoader
  private loadPromise: Promise<any> | null = null
  private retryCount = 0
  private maxRetries = 3

  private constructor() {}

  static getInstance(): MapLoader {
    if (!MapLoader.instance) {
      MapLoader.instance = new MapLoader()
    }
    return MapLoader.instance
  }

  async loadAMapScript(): Promise<any> {
    if (this.loadPromise) {
      return this.loadPromise
    }

    if (window.AMap) {
      return Promise.resolve(window.AMap)
    }

    this.loadPromise = this.loadWithRetry()
    return this.loadPromise
  }

  private async loadWithRetry(): Promise<any> {
    try {
      const AMap = await AMapLoader.load({
        key: '6ff574d8df1f3e3629b6916536482edc',
        version: '2.0',
        plugins: [
          'AMap.ToolBar',
          'AMap.Scale', 
          'AMap.HawkEye',
          'AMap.MapType',
          'AMap.Geolocation',
          'AMap.DistrictSearch',
          'AMap.GeoJSON',
          'AMap.InfoWindow'
        ]
      })
      
      this.retryCount = 0 // 重置重试计数
      console.log('高德地图加载成功')
      return AMap
      
    } catch (error) {
      console.error(`高德地图加载失败 (尝试 ${this.retryCount + 1}/${this.maxRetries}):`, error)
      
      if (this.retryCount < this.maxRetries - 1) {
        this.retryCount++
        this.loadPromise = null
        
        // 延迟重试
        await new Promise(resolve => setTimeout(resolve, 1000 * this.retryCount))
        return this.loadWithRetry()
      } else {
        this.loadPromise = null
        this.retryCount = 0
        throw new Error(`高德地图加载失败，已重试${this.maxRetries}次`)
      }
    }
  }

  reset() {
    this.loadPromise = null
    this.retryCount = 0
  }
}

export default MapLoader.getInstance()
