<template>
  <div class="dashboard-container">
    <!-- 头部标题 -->
    <div class="header">
      <div class="header-bg">
        <h1 class="title">吴桥县供水综合监管系统</h1>
      </div>
    </div>

    <!-- 共享地图容器 -->
    <div class="shared-map-container">
      <div ref="sharedMapContainer" class="shared-map"></div>
      
      <!-- 智慧运营面板 -->
      <SmartOperation 
        ref="smartOperation"
        v-if="activeTab === 'smart'" 
        :map-instance="sharedMap"
        @station-click="handleStationClick"
      />
      
      <!-- 四预分析面板 -->
      <PredictionAnalysis 
        ref="predictionAnalysis"
        v-if="activeTab === 'prediction'" 
        :map-instance="sharedMap"
        @station-click="handleStationClick"
      />
    </div>

    <!-- 底部悬浮tab按钮 -->
    <div class="bottom-tabs floating-tabs">
      <button 
        @click="switchTab('smart')" 
        :class="['tab-button', { active: activeTab === 'smart' }]"
      >
        智慧运营
      </button>
      <button 
        @click="switchTab('prediction')" 
        :class="['tab-button', { active: activeTab === 'prediction' }]"
      >
        四预分析
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, onUnmounted, getCurrentInstance } from 'vue'
import SmartOperation from '@/components/SmartOperation.vue'
import PredictionAnalysis from '@/components/PredictionAnalysis.vue'
import mapLoader from '@/utils/mapLoader'
import { getMapPipelineData, getMapSites } from '@/api/dashboard'

// 响应式数据
const activeTab = ref('smart')
const sharedMapContainer = ref(null)
const sharedMap = ref(null)

// 地图数据
const pipelineData = ref([])
const stationData = ref([])
let pipelineLayer = null
let stationMarkers = []
let countyBoundary = null

// 初始化共享地图
const initSharedMap = async () => {
  try {
    const AMap = await mapLoader.loadAMapScript()
    
    if (sharedMapContainer.value) {
      if (sharedMap.value) {
        sharedMap.value.destroy()
        sharedMap.value = null
      }
      
      const mapInstance = new AMap.Map(sharedMapContainer.value, {
        zoom: 12,
        center: [116.8716, 37.6286],
        mapStyle: 'amap://styles/dark',
        resizeEnable: true,
        viewMode: "2D"
      })

      // 等待地图完全加载后再传递给子组件
      mapInstance.on('complete', () => {
        sharedMap.value = mapInstance
      })

      // 加载基础地图数据
      await loadCountyBoundary()
      await loadMapData()
    }
  } catch (error) {
    console.error('共享地图初始化失败:', error)
  }
}

// 加载吴桥县边界

// const loadCountyBoundary = async () => {
//   try {
//     const AMap = window.AMap
    
//     const districtSearch = new AMap.DistrictSearch({
//       level: 'district',
//       extensions: 'all',
//       subdistrict: 0
//     })

//     return new Promise((resolve, reject) => {
//       districtSearch.search('河北省沧州市吴桥县', (status, result) => {
//         if (status === 'complete' && result.districtList && result.districtList.length > 0) {
//           const district = result.districtList[0]
//           const boundaries = district.boundaries
          
//           if (boundaries && boundaries.length > 0) {
//             const polygons = boundaries.map((boundary) => {
//               return new window.AMap.Polygon({
//                 path: boundary,
//                 strokeColor: '#00d4ff',
//                 strokeWeight: 2,
//                 strokeOpacity: 0.8,
//                 fillColor: '#00d4ff',
//                 fillOpacity: 0.1,
//                 strokeStyle: 'dashed',
//                 zIndex: 10
//               })
//             })
            
//             sharedMap.add(polygons)
//             countyBoundary = polygons
            
//             const center = district.center
//             sharedMap.setCenter([center.lng, center.lat])
//             sharedMap.setZoom(11)
            
//             resolve(polygons)
//           }
//         } else {
//           reject(new Error('搜索失败或未找到吴桥县'))
//         }
//       })
//     })
//   } catch (error) {
//     console.error('边界数据加载失败:', error)
//   }
// }

// 加载吴桥县边界
const loadCountyBoundary = async () => {
  try {
    // 加载GeoJSON文件
    const response = await fetch('/130928.geoJson')
    const geoJsonData = await response.json()
    
    const polygons: any[] = []
    
    // 解析GeoJSON数据
    if (geoJsonData.type === 'FeatureCollection') {
      geoJsonData.features.forEach((feature: any) => {
        // 只取吴桥县
        if(feature.properties.name === '吴桥县') {
          if (feature.geometry.type === 'Polygon') {
            // 处理Polygon类型
            const coordinates = feature.geometry.coordinates[0] // 外环坐标
            const polygon = new window.AMap.Polygon({
              path: coordinates.map((coord: number[]) => [coord[0], coord[1]]),
              strokeColor: '#00d4ff',
              strokeWeight: 2,
              strokeOpacity: 0.8,
              fillColor: '#00d4ff',
              fillOpacity: 0.1,
              strokeStyle: 'dashed',
              zIndex: 10
            })
            polygons.push(polygon)
          } else if (feature.geometry.type === 'MultiPolygon') {
            // 处理MultiPolygon类型
            feature.geometry.coordinates.forEach((polygon: any) => {
              const coordinates = polygon[0] // 外环坐标
              const poly = new window.AMap.Polygon({
                path: coordinates.map((coord: number[]) => [coord[0], coord[1]]),
                strokeColor: '#00d4ff',
                strokeWeight: 2,
                strokeOpacity: 0.8,
                fillColor: '#00d4ff',
                fillOpacity: 0.1,
                strokeStyle: 'dashed',
                zIndex: 10
              })
              polygons.push(poly)
            })
          }
        }
      })
    }
    if (polygons.length > 0) {
      // 添加到地图
      sharedMap.value.add(polygons)
      countyBoundary = polygons
      
      // 调整地图视野
      sharedMap.value.setFitView(polygons)
    }
    
  } catch (error) {
    console.error('GeoJSON边界数据加载失败:', error)
  }
}

// 加载地图数据
const loadMapData = async () => {
  try {
    // 加载管线数据
    const pipelineRes = await getMapPipelineData()
    pipelineData.value = pipelineRes.data || []

    // 加载站点数据
    const stationRes = await getMapSites()
    stationData.value = stationRes.rows || []

    // 渲染管线（两个tab都需要）
    renderPipelines()
  } catch (error) {
    console.error('地图数据加载失败:', error)
  }
}

// 渲染管线
const renderPipelines = () => {
  if (!sharedMap || !pipelineData.value.length) return

  // 清除现有管线图层
  if (pipelineLayer) {
    sharedMap.value.remove(pipelineLayer)
  }

  const polylines = []

  pipelineData.value.forEach((pipeline) => {
    if (pipeline.location) {
      const coordinates = pipeline.location.split('|').map((coord) => {
        const [lng, lat] = coord.split(',').map(Number)
        return [lng, lat]
      })

      if (coordinates.length >= 2) {
        const polyline = new window.AMap.Polyline({
          path: coordinates,
          strokeColor: '#00d4ff',
          strokeWeight: 3,
          strokeOpacity: 0.8,
          strokeStyle: 'solid'
        })
        polylines.push(polyline)
      }
    }
  })

  if (polylines.length > 0) {
    sharedMap.value.add(polylines)
    pipelineLayer = polylines
  }
}

// 切换tab
const switchTab = (tab) => {
  activeTab.value = tab
  
  // 重置地图视野
  if (sharedMap.value) {
    if (countyBoundary && countyBoundary.length > 0) {
      sharedMap.value.setFitView(countyBoundary)
    } else {
      // sharedMap.value.panTo([116.8716, 37.6286], 800)
      sharedMap.value.setCenter([116.8716, 37.6286])
      sharedMap.value.setZoom(12)
    }
  }
}


// 清除站点标记
const clearStationMarkers = () => {
  if (stationMarkers.length > 0) {
    sharedMap.value.remove(stationMarkers)
    stationMarkers = []
  }
}

// 处理站点点击事件
const handleStationClick = (station) => {
  console.log('站点点击:', station)
}

// 向子组件提供地图数据
const getMapData = () => ({
  stationData: stationData.value,
  pipelineData: pipelineData.value
})

// 组件挂载
onMounted(async () => {
  await nextTick()
  await initSharedMap()
})

// 组件卸载
onUnmounted(() => {
  if (sharedMap) {
    sharedMap.value.destroy()
    sharedMap.value = null
  }
})

// 向子组件暴露方法
defineExpose({
  sharedMap,
  getMapData,
  clearStationMarkers,
  stationMarkers
})
</script>

<style lang="scss" scoped>
/* 全局容器 */
.dashboard-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
  color: #ffffff;
  font-family: 'Microsoft YaHei', sans-serif;
  overflow: hidden;
  position: relative;
}

.shared-map-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
}

.shared-map {
  width: 100%;
  height: 100%;
}

.header {
  width: 100%;
  height: 60px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 20;
}

.header-bg {
  margin: 0 auto;
  width: 1920px;
  height: 60px;
  box-sizing: border-box;
  background: url('@/assets/screen-header-bg.png') center center / cover no-repeat;
}

.title {
  margin: 0 auto;
  height: 60px;
  line-height: 60px;
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  text-align: center;
}
/* 悬浮底部tab按钮 */
.floating-tabs {
  position: absolute;
  left: 0;
  bottom: 30px;
  width: 100vw;
  display: flex;
  justify-content: center;
  z-index: 20;
  pointer-events: none;
}
.floating-tabs .tab-button {
  pointer-events: auto;
}

.bottom-tabs {
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.tab-button {
  background: url('@/assets/tab-btn-def.png') no-repeat 0 0;
  background-size: 100% 100%;
  color: #fff;
  padding: 12px 30px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.tab-button.active {
  background: url('@/assets/tab-btn-act.png') no-repeat 0 0;
  background-size: 100% 100%;
  color: #fff;
}

.tab-button:hover:not(.active) {
  // border-color: #00d4ff;
  color: #00d4ff;
}
</style>



