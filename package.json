{"name": "datascreen-wq", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "axios": "^1.6.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.10.4", "vue": "^3.5.17"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "sass-embedded": "^1.89.2", "scss": "^0.2.4", "typescript": "~5.8.3", "unplugin-element-plus": "^0.10.0", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}