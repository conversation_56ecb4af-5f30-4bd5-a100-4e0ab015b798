declare module '*.vue' {
  import { ComponentOptions } from 'vue'
  const componentOptions: ComponentOptions
  export default componentOptions
}

declare module '@amap/amap-jsapi-loader' {
  interface LoadOptions {
    key: string
    version?: string
    plugins?: string[]
    AMapUI?: {
      version?: string
      plugins?: string[]
    }
    Loca?: {
      version?: string
    }
  }
  
  function load(options: LoadOptions): Promise<any>
  export default { load }
}

declare global {
  interface Window {
    AMap: any
    AMapUI: any
    Loca: any
  }
}
