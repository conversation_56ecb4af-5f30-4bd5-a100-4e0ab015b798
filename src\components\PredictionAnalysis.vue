<template>
  <!-- <div class="map-fullscreen"> -->
    <!-- <div ref="predictionMapContainer" class="map"></div> -->
    <!-- 左侧悬浮面板 -->
    <div class="left-panel floating-panel prediction-panel">
      <!-- 设置模型 -->
      <div class="panel-section ctrl-model">
        <div class="panel-header">
          <span>设置模型</span>
        </div>
        <div class="model-form">
          <div class="form-group">
            <label>模型名称</label>
            <ElInput v-model="modelSettings.modelName" type="text" placeholder="请输入" class="form-input"></ElInput>
          </div>
          <div class="form-group">
            <label>模型类型</label>
            <ElSelect v-model="modelSettings.modelType" class="form-select">
              <ElOption label="时间序列" value="time_series" />
              <ElOption label="机器学习" value="machine_learning" />
            </ElSelect>
          </div>
          <div class="form-group">
            <label>预测目标</label>
            <ElSelect v-model="modelSettings.targetField" class="form-select">
              <ElOption label="PH" value="ph_value" />
              <ElOption label="浊度" value="turbidity" />
              <ElOption label="余氯" value="chlorine" />
              <ElOption label="溶解氧" value="dissolved_oxygen" />
              <ElOption label="水温" value="temperature" />
              <ElOption label="电导率" value="conductivity" />
            </ElSelect>
          </div>
          <div class="form-group">
            <label>输入特征字段</label>
            <ElSelect v-model="modelSettings.inputFields" class="form-select">
              <ElOption label="PH" value="ph_value" />
              <ElOption label="浊度" value="turbidity" />
              <ElOption label="余氯" value="chlorine" />
            </ElSelect>
          </div>
          <div class="form-group">
            <label>时间窗口 (h)</label>
            <ElInput v-model="modelSettings.timeWindow" type="text" placeholder="请输入" class="form-input"></ElInput>
          </div>
          <div class="form-group">
            <label>预测窗口 (h)</label>
            <ElInput v-model="modelSettings.forecastWindow" type="text" placeholder="请输入" class="form-input"></ElInput>
          </div>
          <div class="form-group">
            <label>备注</label>
            <ElInput type="textarea" v-model="modelSettings.notes" placeholder="请输入" class="form-textarea"></ElInput>
          </div>
          <div class="form-actions">
            <button @click="startSimulation" class="btn-secondary">开始模拟</button>
            <button @click="resetForm" class="btn-secondary">取消</button>
            <button class="btn-secondary">查看模拟结果</button>
          </div>
        </div>
      </div>
      <!-- 图例 -->
      <div class="panel-section legend-section">
        <div class="legend-header">
          <span class="legend-title-text">图例</span>
        </div>
        <div class="legend-grid legend-grid-custom">
          <div class="legend-card legend-card-custom" v-for="item in legendItems" :key="item.title">
            <img class="legend-card-icon" src="/src/assets/legend-icon.png" alt="icon" />
            <div class="legend-card-content" style="background:url('/src/assets/legend-bg.png') center center/cover no-repeat;">
              <div class="legend-card-title">{{ item.title }}</div>
              <div class="legend-card-sub">{{ item.unit }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 地图信息窗体 -->
    <div v-if="showInfoWindow" class="map-info-window" :style="infoWindowStyle">
      <div class="info-window-content">
        <div class="info-window-header">
          <h3>{{ selectedStation.stationName }}</h3>
          <button class="info-close-btn" @click="closeInfoWindow">×</button>
        </div>
        <div class="info-window-body">
          <div class="station-chart-right">
            <div ref="stationChart" style="width: 100%; height: 200px;"></div>
          </div>
        </div>
        <div class="info-window-arrow"></div>
      </div>
    </div>
  <!-- </div> -->
</template>
<script setup lang="ts">
import { ref, nextTick, onUnmounted } from 'vue'
import { ElSelect, ElOption, ElInput } from 'element-plus'
import { startPredictionSimulation } from '@/api/dashboard'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

const modelSettings = ref({
  modelName: '',
  modelType: '',
  targetField: '',
  inputFields: '',
  timeWindow: '',
  forecastWindow: '',
  notes: ''
})
const legendItems = [
  { title: 'pH', unit: '无量纲' },
  { title: '电导率', unit: 'μS/cm' },
  { title: '浊度', unit: 'NTU' },
  { title: '溶解氧', unit: 'mg/L' },
  { title: '余氯', unit: 'mg/L' },
  { title: '水温', unit: '℃' }
]
// 弹窗相关
const showInfoWindow = ref(false)
const selectedStation = ref({})
const stationChart = ref(null)
const infoWindowStyle = ref({})

// 接收父组件传递的地图实例
const props = defineProps({
  mapInstance: {
    type: Object,
    default: null
  }
})
// 向父组件发送事件
const emit = defineEmits(['station-click'])

let stationMarkers:any[] = []
// 渲染站点标记
const renderStations = (data) => {
  if (!props.mapInstance) return
  try {
    const stationData = data.stationPredictions || []

    if (!stationData.length) return
    // 清除现有标记
    if (stationMarkers.length > 0) {
      props.mapInstance.remove(stationMarkers)
      stationMarkers = []
    }

    stationData.forEach((station) => {
      if (station.longitude && station.latitude) {
        const marker = new window.AMap.Marker({
          position: [station.longitude, station.latitude],
          content: createStationMarkerContent(station),
          anchor: 'center',
          clickable: true,
          title: station.stationName
        })

        marker.on('click', () => {
          openStationModal(station)
          emit('station-click', station)
        })

        stationMarkers.push(marker)
      }
    })

    if (stationMarkers.length > 0) {
      props.mapInstance.add(stationMarkers)
    }
  } catch (error) {
    console.error('站点数据加载失败:', error)
  }
}
// 创建自定义标记内容
const createStationMarkerContent = (station: any) => {
  const bgColor = '#00d4ff'
  const shadowColor = 'rgba(107, 207, 127, 0.4)'
  
  return `
    <div style="
      background: ${bgColor};
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: bold;
      white-space: nowrap;
      box-shadow: 0 2px 8px ${shadowColor};
      border: 1px solid ${bgColor};
      transform: translate(-50%, -100%);
      cursor: pointer;
    ">
      ${station.stationName}
    </div>
  `
}
// 打开站点信息窗体
const openStationModal = async (station:any) => {
  selectedStation.value = station

  // 平滑移动地图，将marker置于中央
  const position = [station.longitude, station.latitude]
  props.mapInstance.panTo(position, 500) // 500ms动画时间

  // 计算信息窗体位置
  setTimeout(() => {
    const pixel = props.mapInstance.lngLatToContainer(position)
    infoWindowStyle.value = {
      position: 'absolute',
      left: `${pixel.x - 200}px`, // 窗体宽度的一半
      top: `${pixel.y - 280}px`,  // 窗体高度 + 箭头高度
      zIndex: 1000
    }

    showInfoWindow.value = true

    // 初始化图表
    nextTick(() => {
      initStationChart()
    })
  }, 500) // 等待地图移动完成
}
// 关闭信息窗体
const closeInfoWindow = () => {
  showInfoWindow.value = false
  selectedStation.value = {}
  // stationWaterQualityData.value = []
}
// 初始化站点图表
const initStationChart = () => {
  if (!stationChart.value) return

  // 销毁现有图表实例
  const existingChart = echarts.getInstanceByDom(stationChart.value)
  if (existingChart) {
    existingChart.dispose()
  }

  const chart = echarts.init(stationChart.value)

  const trends = selectedStation.value.predictions || []

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00d4ff',
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: trends.map((item:any) => dayjs(item.forecastTime).format('MM-DD HH:mm')),
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: {
        color: '#00d4ff',
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: {
        color: '#00d4ff',
        fontSize: 10
      },
      splitLine: { lineStyle: { color: '#2a3441' } }
    },
    series: [
      {
        name: '模拟结果',
        type: 'line',
        data: trends.map((item:any) => item.predictValue || 0),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00d4ff' },
            { offset: 1, color: '#0099cc' }
          ])
        }
      }
    ]
  }

  chart.setOption(option)
}
// 组件卸载时清除marker
onUnmounted(() => {
  clearMarkers()
})

const startSimulation = async () => {
  try {
    const res = await startPredictionSimulation(modelSettings.value)
    if (props.mapInstance) {
      // 添加预测结果的可视化
      // renderPredictionResults(res.data)
      renderStations(res.data)
    }
  } catch (error) {
    console.error('模拟失败:', error)
  }
}
const resetForm = () => {
  modelSettings.value = {
    modelName: '',
    modelType: '',
    targetField: '',
    inputFields: '',
    timeWindow: '',
    forecastWindow: '',
    notes: ''
  }
}

// 清除marker的方法
const clearMarkers = () => {
  if (stationMarkers.length > 0 && props.mapInstance) {
    props.mapInstance.remove(stationMarkers)
    stationMarkers = []
  }
}

</script>
<style lang="scss" scoped>
:deep(.el-select__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: none;
}
:deep(.el-select__wrapper:hover) {
  box-shadow: none;
}
:deep(.el-select__placeholder) {
  color: #fff;
}
:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: none;
}
:deep(.el-input__inner) {
  color: #fff;
}
:deep(.el-textarea__inner) {
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: none;
}
:deep(.el-textarea__inner) {
  color: #fff;
}
.map-fullscreen {
  position: absolute;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
}
.map-fullscreen .map {
  width: 100vw;
  height: 100vh;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}
/* 悬浮面板样式 */
.floating-panel {
  position: absolute;
  top: 80px;
  z-index: 10;
  background: none;
  pointer-events: none;
}
.left-panel.floating-panel {
  left: 30px;
  width: 520px;
  pointer-events: none;
}
.floating-panel .panel-section {
  pointer-events: auto;
}
.panel-section {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  padding: 15px;
  width: 520px;
  height: 298px;
  box-sizing: border-box;
  background: url('@/assets/chart-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
}
.ctrl-model {
  padding: 25px 15px;
  height: auto;
  background: url('@/assets/ctrl-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
}
.panel-header {
  color: #00d4ff;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  text-align: center;
}
.model-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.form-group label {
  color: #fff;
  font-size: 14px;
}
.form-select, .form-input, .form-textarea {
  width: 100%;
}
.form-input::placeholder {
  color: #fff;
}
.form-textarea {
  resize: vertical;
  min-height: 60px;
}
.form-actions {
  display: flex;
  justify-content: space-around;
  // gap: 8px;
  // flex-wrap: wrap;
}
.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #1890ff;
  color: #fff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}
.legend-section {
  box-shadow: none;
}
.legend-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  position: relative;
}
.legend-title-text {
  color: #fff;
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 8px;
}
.legend-grid-custom {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 18px 24px;
  padding: 0 10px 10px 10px;
}
.legend-card-custom {
  display: flex;
  align-items: center;
  gap: 10px;
  background: none;
  border-radius: 8px;
  box-shadow: none;
  padding: 0;
}
.legend-card-icon {
  width: 48px;
  height: 48px;
  display: block;
}
.legend-card-content {
  flex: 1;
  min-width: 120px;
  min-height: 48px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border-radius: 6px;
  padding: 4px 12px;
  box-sizing: border-box;
}
.legend-card-title {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 2px;
  margin-bottom: 2px;
}
.legend-card-sub {
  color: #00d4ff;
  font-size: 13px;
  font-weight: normal;
}
/* 地图信息窗体样式 */
.map-info-window {
  position: absolute;
  width: 400px;
  background: linear-gradient(135deg, rgba(26, 35, 50, 0.95) 0%, rgba(15, 20, 25, 0.95) 100%);
  border: 2px solid #00d4ff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 212, 255, 0.4);
  backdrop-filter: blur(10px);
  z-index: 1000;
}

.info-window-content {
  position: relative;
}

.info-window-header {
  background: rgba(0, 212, 255, 0.15);
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.info-window-header h3 {
  color: #00d4ff;
  font-size: 16px;
  font-weight: bold;
  margin: 0;
}

.info-close-btn {
  background: none;
  border: none;
  color: #00d4ff;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.info-close-btn:hover {
  background: rgba(0, 212, 255, 0.2);
}

.info-window-body {
  padding: 16px;
  display: flex;
  gap: 16px;
}

.station-info-left {
  flex: 1;
  min-width: 160px;
}

.station-status {
  display: inline-block;
  padding: 3px 10px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: bold;
  margin-bottom: 12px;
}

.station-status.normal {
  background: rgba(107, 207, 127, 0.2);
  color: #6bcf7f;
  border: 1px solid #6bcf7f;
}

.station-status.abnormal {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid #ff6b6b;
}

.station-detail {
  margin-bottom: 16px;
}

.station-detail p {
  color: #fff;
  font-size: 12px;
  margin: 6px 0;
  line-height: 1.4;
}

.station-detail strong {
  color: #00d4ff;
  margin-right: 6px;
}

.station-buttons {
  display: flex;
  gap: 6px;
}

.btn-tab {
  padding: 6px 12px;
  border: 1px solid #2a3441;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
}

.btn-tab.active,
.btn-tab:hover {
  background: #00d4ff;
  border-color: #00d4ff;
  color: #fff;
}

.station-chart-right {
  flex: 1;
  min-width: 200px;
}

.info-window-arrow {
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
  border-top: 12px solid #00d4ff;
}

.info-window-arrow::before {
  content: '';
  position: absolute;
  top: -14px;
  left: -10px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid rgba(26, 35, 50, 0.95);
}
</style>



