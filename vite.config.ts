import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import ElementPlus from 'unplugin-element-plus/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    ElementPlus({})
  ],
  // 路径别名配置
  resolve: {
    alias: {
      '@': '/src',
      'components': '/src/components',
      'views': '/src/views',
      'assets': '/src/assets',
      'utils': '/src/utils',
      'store': '/src/store',
      'api': '/src/api',
      'router': '/src/router',
      'styles': '/src/styles',
      'mixins': '/src/mixins',
      'directives': '/src/directives',
      'composables': '/src/composables',
      'plugins': '/src/plugins',
    }
  },
  // 开发服务器配置
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://10.6.5.245:80',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
})
