import request from '@/utils/request'

interface WorkOrderParams {
  createTime: string
  status: number
}

// 综合态势
export const getComprehensiveSituation = () => {
  return request.get('/prod-api/water/meter/statistics')
}
// 水质告警
export const getWaterQualityAlerts = () => {
  return request.get('/prod-api/water/alarm/active?pageNum=1&pageSize=20')
}

// 工单数据接口
export const getWorkOrderData = (params: WorkOrderParams) => {
  return request.get('/prod-api/flow/record/list', {
    params
  })
}
// 基础统计
export const getBasicStatistics = () => {
  return request.get('/prod-api/water/device/list?pageNum=1&pageSize=9999')
}

// 监测设备列表
export const getDeviceList = (params: { stationId: string }) => {
  return request.get('/prod-api/water/device/list?pageSize=100', {
    params
  })
}
// 流量数据
export const getFlowData = (params: { startTime: string; endTime: string, deviceCode: string }) => {
  return request.get('/prod-api/water/meter/flow/history?orderByColumn=monitor_time&isAsc=desc&pageSize=100', {
    params,
  })
}
// 压力数据
export const getPressureData = (params: { startTime: string; endTime: string, deviceCode: string }) => {
  return request.get('/prod-api/water/meter/pressure/history?orderByColumn=monitor_time&isAsc=desc&pageSize=100', {
    params,
  })
}
// 水质数据
export const getWaterQualityData = (params: { beginTime?: string; endTime?: string, deviceCode: string }) => {
  return request.get(`/prod-api/water/quality/data/analysis/${params.deviceCode}`, {
    params: {
      ...params,
    }
  })
}
// 水站列表
export const getWaterStationList = () => {
  return request.get('/prod-api/water/station/list?pageNum=1&pageSize=1000')
}
// 营收情况
export const getRevenueData = (params: { startTime: string; endTime: string }) => {
  return request.get('/prod-api/water/usage/report/statistics', {
    params,
  })
}

// 地图管线数据
export const getMapPipelineData = () => {
  return request.get('/prod-api/water/pipeline/list')
}
// 地图站点数据接口
export const getMapSites = () => {
  return request.get('/prod-api/water/station/list?pageNum=1&pageSize=1000&orderByColumn=station_id&isAsc=asc')
}
// 预测模拟接口
export const startPredictionSimulation = (data: any) => {
  return request.post('/prod-api/water/forecast/model/instant-predict', data)
}